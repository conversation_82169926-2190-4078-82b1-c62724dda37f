import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';

interface StockMarchEntry {
  _id: string;
  receiveNo: string;
  receivingDate: string;
  weaverName: string;
  design: string;
  size: string;
  amount: string | number;
  rate?: string | number;
}

interface UpdateResult {
  receiveNo: string;
  weaverName: string;
  originalSize: string;
  newSize: string;
  originalAmount: string | number;
  newAmount: string | number;
  originalRate?: string | number;
  newRate?: string | number;
  needsUpdate: boolean;
}

@Component({
  selector: 'app-format-stock-march',
  template: '<div>Format Stock March Component</div>',
  styles: []
})
export class FormatStockMarchComponent implements OnInit {
  stockMarchEntries: StockMarchEntry[] = [];
  updateResults: UpdateResult[] = [];
  isLoading = false;
  isUpdating = false;
  showResults = false;
  entriesNeedingUpdate = 0;
  entriesAlreadyCorrect = 0;

  constructor(private http: HttpClient) {}

  ngOnInit(): void {
    this.loadStockMarchEntries();
  }

  loadStockMarchEntries(): void {
    this.isLoading = true;
    
    // Load carpet received data to find Stock March entries
    this.http.get<StockMarchEntry[]>('http://localhost:2000/api/phase-four/carpetReceived/carpetReceived')
      .subscribe({
        next: (data) => {
          // Filter for Stock March entries (H- format or containing "Stock March")
          this.stockMarchEntries = data.filter(entry => 
            entry.receiveNo?.startsWith('H-') || 
            entry.weaverName?.toLowerCase().includes('stock march')
          );
          
          console.log('Stock March entries found:', this.stockMarchEntries.length);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading stock march entries:', error);
          this.isLoading = false;
        }
      });
  }

  formatSize(size: string): string {
    if (!size) return size;

    // Handle patterns like "5 6 x 7 9" -> "5.6x7.9"
    const spacedPattern = /(\d+)\s+(\d+)\s*[Xx]\s*(\d+)\s+(\d+)/;
    const spacedMatch = size.match(spacedPattern);

    if (spacedMatch) {
      const width1 = spacedMatch[1];
      const width2 = spacedMatch[2];
      const length1 = spacedMatch[3];
      const length2 = spacedMatch[4];

      return `${width1}.${width2}x${length1}.${length2}`;
    }

    // Handle normal patterns like "23 x 45" or "2.3 x 4.5"
    const sizePattern = /(\d+(?:\.\d+)?)\s*[Xx]\s*(\d+(?:\.\d+)?)/;
    const match = size.match(sizePattern);

    if (match) {
      const width = match[1];
      const length = match[2];

      // Only format multi-digit numbers without decimal (2 digits or more)
      // Single digits and numbers with decimals remain unchanged
      const formattedWidth = (width.includes('.') || width.length === 1) ? width : this.addDecimalToMultiDigit(width);
      const formattedLength = (length.includes('.') || length.length === 1) ? length : this.addDecimalToMultiDigit(length);

      return `${formattedWidth}x${formattedLength}`;
    }

    return size;
  }

  addDecimalToMultiDigit(number: string): string {
    if (number.length <= 1) return number;

    // For multi-digit numbers, add decimal after first digit
    // Examples:
    // "46" -> "4.6"
    // "311" -> "3.11"
    // "66" -> "6.6"
    // "13" -> "1.3"
    // "110" -> "1.10"
    const firstDigit = number.charAt(0);
    const remainingDigits = number.substring(1);

    // If remaining digits are all zeros, format properly
    if (remainingDigits === '0') {
      return firstDigit + '.0';
    }

    return firstDigit + '.' + remainingDigits;
  }

  previewChanges(): void {
    this.updateResults = [];
    this.entriesNeedingUpdate = 0;
    this.entriesAlreadyCorrect = 0;

    this.stockMarchEntries.forEach(entry => {
      const newSize = this.formatSize(entry.size);
      const newAmount = 0; // Set all amounts to zero
      const newRate = 0; // Set all rates to zero

      const needsUpdate = entry.size !== newSize ||
                         (entry.amount !== 0 && entry.amount !== '0') ||
                         (entry.rate && entry.rate !== 0 && entry.rate !== '0');

      if (needsUpdate) {
        this.entriesNeedingUpdate++;
      } else {
        this.entriesAlreadyCorrect++;
      }

      this.updateResults.push({
        receiveNo: entry.receiveNo,
        weaverName: entry.weaverName,
        originalSize: entry.size,
        newSize: newSize,
        originalAmount: entry.amount,
        newAmount: newAmount,
        originalRate: entry.rate,
        newRate: newRate,
        needsUpdate: !!needsUpdate
      });
    });

    this.showResults = true;
  }

  updateStockMarchEntries(): void {
    if (confirm('Are you sure you want to update all Stock March entries? This action cannot be undone.')) {
      this.isUpdating = true;

      const updatePromises = this.stockMarchEntries.map(entry => {
        const updatedEntry = {
          ...entry,
          size: this.formatSize(entry.size),
          amount: 0,
          rate: 0
        };

        return this.http.put(`http://localhost:2000/api/phase-four/carpetReceived/carpetReceived/${entry._id}`, updatedEntry);
      });
      
      Promise.all(updatePromises).then(() => {
        this.isUpdating = false;
        alert('All Stock March entries have been updated successfully!');
        this.loadStockMarchEntries(); // Reload data
        this.showResults = false;
      }).catch(error => {
        console.error('Error updating entries:', error);
        this.isUpdating = false;
        alert('Error updating entries. Please try again.');
      });
    }
  }

  getExamples(): string[] {
    return [
      '"46 x 66" → "4.6 x 6.6"',
      '"311 x 511" → "3.11 x 5.11"',
      '"13 x 110" → "1.3 x 1.10"',
      '"12x60" → "1.2 x 6.0"',
      '"7 x 5" → "7 x 5" (unchanged)',
      '"8 x 11" → "8 x 1.1"',
      'Amount: Any value → 0'
    ];
  }
}
