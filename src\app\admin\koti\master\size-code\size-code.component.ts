import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import Swal from 'sweetalert2';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
export interface SizeList {
  index: number;
  id: string;
  size: string;
  // unit: string;
  sizeCode: string;
  sequenceCode: string;
}

const ELEMENT_DATA2: SizeList[] = [];
@Component({
  selector: 'app-size-code',
  templateUrl: './size-code.component.html',
  styleUrl: './size-code.component.css'
})
export class SizeCodeComponent implements OnInit {

  displayedColumns: string[] = ['id', 'size', 'sizeCode', 'sequenceCode'];
  dataSource = new MatTableDataSource(ELEMENT_DATA2);
  frmSizeCode!: FormGroup;


  sizeList: any = [];
  newAssignData: any = [];
  uniqueSizeList: any = [];

  constructor(private _services: ImporterService, private _fb: FormBuilder,
  
    @Optional() public dialogRef: MatDialogRef<SizeCodeComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) { }
  ngOnInit(): void {

    this.frmSizeCode = this._fb.group({
      size: [],
      unit: [],
      sizeCode: [],
      sequenceCode: [],
    })
    this.getAllExcelData();
    this.getSizeList();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;



  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  getAllExcelData() {
    this._services.getAllExcelList('').subscribe((resp: any) => {
      debugger
      console.log(resp);

      resp.map((x: any) => {
        this.sizeList.push({
          size: x.Width + 'x' + x.Length
        })
      })



      const uniqueSize = new Set();
      this.sizeList.forEach((item: any) => {
        uniqueSize.add(item.size);
      });
      debugger
      this.uniqueSizeList = Array.from(uniqueSize);
    })
  }

  getSize(event: any) {
    debugger
    let x = event.split(/[Xx]/);
    let totalArea= parseInt(x[0]) * parseInt(x[1]) /10000;
    this.frmSizeCode.get('unit')?.setValue(totalArea.toFixed(2));
  }
  addSize() {
    debugger
    console.log(this.frmSizeCode.value)
    let formData = this.frmSizeCode.value

    this._services.addSizeCode(formData).subscribe((resp: any) => {
      this.newAssignData.push(formData);
      Swal.fire({
        title: "Success!",
        text: "Code has been assigned successfull!",
        icon: "success"
      });
      this.getSizeList();

    }, (error) => {
      Swal.fire({
        title: "Warning!",
        text: "Something went wrong!",
        icon: "warning"
      });
    })
  }

  getSizeList() {
    this._services.getAllSizeList().subscribe((resp: any) => {
      if (resp) {
        ELEMENT_DATA2.length = 0;
        resp.map((x: any, i: number) => {
          ELEMENT_DATA2.push({
            id: x._id,
            size: x.size,
            index: i + 1,
            sizeCode: x.sizeCode,
            sequenceCode: x.sequenceCode,
          })
        })
        this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
        this.ngAfterViewInit();
        return;
      }
    });
  }

  closeDialogWithData(): void {
    let size = { sizelist: this.newAssignData }
    this.dialogRef.close(size);
  }
}
