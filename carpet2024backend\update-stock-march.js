const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

// Format size function - keep neat and clean format like "7 X 42"
const formatSize = (size) => {
  if (!size) return size;

  // Handle patterns like "5 6 x 7 9" -> "5.6 X 7.9"
  const spacedPattern = /(\d+)\s+(\d+)\s*[Xx]\s*(\d+)\s+(\d+)/;
  const spacedMatch = size.match(spacedPattern);

  if (spacedMatch) {
    const width1 = spacedMatch[1];
    const width2 = spacedMatch[2];
    const length1 = spacedMatch[3];
    const length2 = spacedMatch[4];

    return `${width1}.${width2} X ${length1}.${length2}`;
  }

  // Handle normal patterns and ensure proper spacing like "7 X 42"
  const sizePattern = /(\d+(?:\.\d+)?)\s*[Xx]\s*(\d+(?:\.\d+)?)/;
  const match = size.match(sizePattern);

  if (match) {
    const width = match[1];
    const length = match[2];

    // Only format multi-digit numbers without decimal
    const formattedWidth = (width.includes('.') || width.length === 1) ? width : addDecimalToMultiDigit(width);
    const formattedLength = (length.includes('.') || length.length === 1) ? length : addDecimalToMultiDigit(length);

    return `${formattedWidth} X ${formattedLength}`;
  }

  return size;
};

const addDecimalToMultiDigit = (number) => {
  if (number.length <= 1) return number;
  
  const firstDigit = number.charAt(0);
  const remainingDigits = number.substring(1);
  
  if (remainingDigits === '0') {
    return firstDigit + '.0';
  }
  
  return firstDigit + '.' + remainingDigits;
};

async function updateStockMarch() {
  try {
    console.log('🔍 Finding Stock March entries...');
    
    // Find Stock March entries - including March format and H- format
    const stockMarchEntries = await CarpetReceived.find({
      $or: [
        { receiveNo: { $regex: '^H-', $options: 'i' } },
        { receiveNo: { $regex: '^March', $options: 'i' } },
        { weaverName: { $regex: 'stock march', $options: 'i' } }
      ]
    });
    
    console.log(`📊 Found ${stockMarchEntries.length} Stock March entries`);
    
    let updatedCount = 0;
    
    for (const entry of stockMarchEntries) {
      const originalSize = entry.size;
      const newSize = formatSize(entry.size);
      
      const needsUpdate = originalSize !== newSize || 
                         (entry.amount && entry.amount !== 0) ||
                         (entry.rate && entry.rate !== 0);
      
      if (needsUpdate) {
        await CarpetReceived.updateOne(
          { _id: entry._id },
          { 
            $set: { 
              size: newSize,
              amount: 0,
              rate: 0
            }
          }
        );
        
        updatedCount++;
        console.log(`✅ Updated: ${entry.receiveNo} - Size: "${originalSize}" -> "${newSize}"`);
      }
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries out of ${stockMarchEntries.length} found`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

updateStockMarch();
