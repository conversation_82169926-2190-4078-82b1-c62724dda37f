const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

// Format size function - keep neat and clean format like "7 X 42"
const formatSize = (size) => {
  if (!size) return size;

  // Handle patterns like "5 6 x 7 9" -> "5.6 X 7.9"
  const spacedPattern = /(\d+)\s+(\d+)\s*[Xx]\s*(\d+)\s+(\d+)/;
  const spacedMatch = size.match(spacedPattern);

  if (spacedMatch) {
    const width1 = spacedMatch[1];
    const width2 = spacedMatch[2];
    const length1 = spacedMatch[3];
    const length2 = spacedMatch[4];

    return `${width1}.${width2} X ${length1}.${length2}`;
  }

  // Handle normal patterns and ensure proper spacing like "7 X 42"
  const sizePattern = /(\d+(?:\.\d+)?)\s*[Xx]\s*(\d+(?:\.\d+)?)/;
  const match = size.match(sizePattern);

  if (match) {
    const width = match[1];
    const length = match[2];

    // Only format multi-digit numbers without decimal
    const formattedWidth = (width.includes('.') || width.length === 1) ? width : addDecimalToMultiDigit(width);
    const formattedLength = (length.includes('.') || length.length === 1) ? length : addDecimalToMultiDigit(length);

    // Ensure neat spacing: single space before and after X
    return `${formattedWidth} X ${formattedLength}`;
  }

  return size;
};

const addDecimalToMultiDigit = (number) => {
  if (number.length <= 1) return number;
  
  const firstDigit = number.charAt(0);
  const remainingDigits = number.substring(1);
  
  if (remainingDigits === '0') {
    return firstDigit + '.0';
  }
  
  return firstDigit + '.' + remainingDigits;
};

async function updateStockMarch() {
  try {
    console.log('🔍 Finding Stock March entries...');
    
    // Find Stock March entries - including March format and H- format
    const stockMarchEntries = await CarpetReceived.find({
      $or: [
        { receiveNo: { $regex: '^H-', $options: 'i' } },
        { receiveNo: { $regex: '^March', $options: 'i' } },
        { weaverName: { $regex: 'stock march', $options: 'i' } }
      ]
    });
    
    console.log(`📊 Found ${stockMarchEntries.length} Stock March entries`);
    
    let updatedCount = 0;
    
    for (const entry of stockMarchEntries) {
      const originalSize = entry.size;
      const newSize = formatSize(entry.size);

      // Check if issueNo object needs updating
      const issueNoNeedsUpdate = entry.issueNo && (
        (entry.issueNo.size && (
          entry.issueNo.size.sizeInYard !== newSize ||
          entry.issueNo.size.sizeinMeter !== newSize
        )) ||
        (entry.issueNo.rate && entry.issueNo.rate !== '0') ||
        (entry.issueNo.amount && entry.issueNo.amount !== '0')
      );

      const needsUpdate = originalSize !== newSize ||
                         (entry.amount && entry.amount !== 0) ||
                         (entry.rate && entry.rate !== 0) ||
                         issueNoNeedsUpdate;

      if (needsUpdate) {
        const updateData = {
          size: newSize,
          amount: 0,
          rate: 0
        };

        // Update issueNo object if it exists
        if (entry.issueNo) {
          updateData['issueNo.size.sizeInYard'] = newSize;
          updateData['issueNo.size.sizeinMeter'] = newSize;
          updateData['issueNo.rate'] = '0';
          updateData['issueNo.amount'] = '0';
        }

        await CarpetReceived.updateOne(
          { _id: entry._id },
          { $set: updateData }
        );

        updatedCount++;
        console.log(`✅ Updated: ${entry.receiveNo} - Size: "${originalSize}" -> "${newSize}", Rate & Amount: 0`);
      }
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries out of ${stockMarchEntries.length} found`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

updateStockMarch();
