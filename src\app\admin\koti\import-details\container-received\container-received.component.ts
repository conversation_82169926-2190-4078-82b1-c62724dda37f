import { Component, OnInit } from '@angular/core';
import { AfterViewInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ImporterService } from '../../../../services/importer.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { Observable, forkJoin, map } from 'rxjs';
import { error } from 'console';
import Swal from 'sweetalert2';
import { MatDialog } from '@angular/material/dialog';
import { QualityDesignCodeComponent } from '../../master/quality-design-code/quality-design-code.component';
import { SizeCodeComponent } from '../../master/size-code/size-code.component';



export interface invoiceData {
  Sno: number;
  GerCarpetNo: number;
  QualityDesign: string;
  QCode: string;
  Color: string;
  CCode: string;
  Size: string;
  SCore: string;
  Area: string;
  Rate: string;
  Amount: number;
  EvKPrice: number;
  InvoiceNo: string;
  ImporterCode: string;
  Remarks: string;
}



const ELEMENT_DATA2: invoiceData[] = [];


@Component({
  selector: 'app-container-received',
  templateUrl: './container-received.component.html',
  styleUrl: './container-received.component.css'
})
export class ContainerReceivedComponent implements OnInit {
  ImportNumber = 'option2';
  ExpensesAmount = 'option1';
  // TotalArea = 'option2';

  bcrNo:any;
  displayedColumns: string[] = ['Sno',  'GerCarpetNo', 'select', 'QualityDesign', 'QCode', 'Color', 'CCode', 'Size', 'SCore', 'Area', 'Rate', 'EvKPrice', 'Amount', 'InvoiceNo', 'ImporterCode', 'Remarks'];
  dataSource = new MatTableDataSource<invoiceData>();
  selection = new SelectionModel<invoiceData>(true, []);
  totalEspPrice: any;

  @ViewChild(MatSort)
  sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  excelArray: any = [];
  pdf: any;

  constructor(private _services: ImporterService, private _fb: FormBuilder, public matDig: MatDialog) { }
  frmContainerRecieved!: FormGroup;

  sizeList: any = [];
  _containerNo: string = ';'
  ngOnInit() {
    this.frmContainerRecieved = this._fb.group({
      expensesAmount: ['', Validators.required],
      impoterName: ['', Validators.required],
      totalArea: ['', Validators.required],
      espPrice: ['', Validators.required],
      blPdf: ['', Validators.required],
    })
    this.getAllSize();
    this.containerNo();
    this.getAllExcelData();
    this.getAllBarCode();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    // this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  uploadpdf(data: any) {
    let file = data.target.files[0];
    this._services.uploadBillPdf(file).subscribe((res: any) => {
      debugger
      this.pdf = res._id
      if (this.pdf) {
        this.frmContainerRecieved.get('blPdf')?.setValue(this.pdf);
      }
    }, (err: any) => {
      console.log(err)
    }
    )


  }
  addReceived() {
    console.log(this.frmContainerRecieved.value);
    let formData = this.frmContainerRecieved.value;

    if (this.frmContainerRecieved.valid) {
      debugger
      formData['containerItem'] = this.selection.selected;
      if (formData.containerItem.length == 0) {
        Swal.fire(
          'Warning!',
          'Please select items',
          'warning'
        )
      } else {
        this._services.addContainerRecieved(formData).subscribe((resp: any) => {
          Swal.fire(
            'Success!',
            'Data has been saved successfully',
            'success'
          )
        }, (error) => {
          Swal.fire(
            'warning!',
            'Failed',
            'warning'
          )
        })
      }

    }
  }
  containerList: any = [];


  containerNo() {
    this._services.getAllContainerDespatche().subscribe((resp: any) => {

      // Extract importer numbers from the response and push them into impotererNo array
      resp.forEach((val: any) => {

        // Extract numeric part of the importer number
        let importcode = val.impoterNo.split('/')[0];

        const numericPart = parseInt(importcode.split('-')[1]); // Assuming the format is "KOTI-XXXX"
        this.containerList.push({ original: importcode, numeric: numericPart });
      });

      // Sort impotererNo array based on the numeric part in descending order
      this.containerList.sort((a: any, b: any) => b.numeric - a.numeric);

      // Reformat the sorted elements back into the original format
      this.containerList = this.containerList.map((entry: any) => entry.original);


      this.containerList = [...new Set(this.containerList)];

    });

  }

  getAllExcelData() {
    this._services.getAllExcelList('').subscribe((resp: any) => {

      this.excelArray = resp;

    })
  }
  containerData: any = [];
  QDList: any = [];
  totalArea: number = 0;
  _importerPriceDetails: any;
  getContainerData(containerNo: string) {

    this.getPrice(containerNo).subscribe((priceList: any[]) => {
      // Log the priceList data

      this._importerPriceDetails = priceList;
      console.log('Importer Price List:', priceList);
    }, (error: any) => {
      // Handle any errors that occur during the subscription
      console.error('Error occurred:', error);
    });

    this._services.getAllQualityDesign().subscribe((resp: any) => {
      this.QDList = resp;
    });
    setTimeout(() => {

      this.containerData = this.excelArray.filter((x: any) => {
        return x.containerNo.toLowerCase() === containerNo.toLowerCase();
      });

      debugger
      console.log(this.containerData);
      ELEMENT_DATA2.length = 0;
      let bcr: number = 5000000;
      this.bcrNo =this.bcrNo.sort((a:any,b:any)=>  b-a)
      let barcode =this.bcrNo[0];
      // Iterate over each item in containerData and map it
      this.containerData.forEach((x: any, ind: number) => {
        let newQD = '';
        let qdCode = '';
        let newColor = '';
        let colorCode = '';

        let newSizes = '';
        let sizeCodes = '';
        let rate;
        
        debugger
        // Iterate over each item in QDList to find matches
        this.QDList.forEach((j: any) => {
          let qdData = j.oldQualityandDesign.split(' ');
          if (qdData[0] == x.Quality && qdData[1] == x.Design) {
            newQD = j.newQualityandDesign;
            qdCode = j.qualityCodeandDesign;
          }

          if (j.oldColor == x.Colour) {
            newColor = j.newColor;
            colorCode = j.colorCode;
          }
        });

        this.sizeList.forEach((i: any) => {
          if (i.size == x.Length + 'x' + x.Width) {

            newSizes = i.size;
            sizeCodes = i.sizeCode
          }
        });



        this._importerPriceDetails.forEach((p: any) => {
          debugger;
          if (p.importerCode === x.ImpoterCode) {
            p.price.forEach((z: any) => {
              if (
                z.quality.toLowerCase() === x.Quality.toLowerCase() &&
                z.design.toLowerCase() === x.Design.toLowerCase()
              ) {
                rate = z.orderPrice;
              }
            });
          }


        });

        // Push mapped data to ELEMENT_DATA2 array
        this.totalArea = this.totalArea + parseFloat(x.Area);
        debugger
        
        
        let _bcr =  barcode? ++barcode : ++bcr
        ELEMENT_DATA2.push({
          Sno: ind + 1,
          GerCarpetNo: _bcr,
          QualityDesign: newQD ? newQD : x.Quality + ' ' + x.Design,
          QCode: qdCode ? qdCode : '',
          Color: newColor ? newColor : x.Colour,
          CCode: colorCode ? colorCode : '',
          Size:  x.Width +' X '+x.Length,
          SCore: sizeCodes,
          Area: x.Area,
          Rate: rate ? rate : '0',
          EvKPrice: 0.00,
          Amount: 0.00,
          InvoiceNo: x.InvoiceNo,
          ImporterCode: x.ImpoterCode,
          Remarks: ''
        });
      });

      this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
      this.ngAfterViewInit();
      this.frmContainerRecieved.get('totalArea')?.setValue(this.totalArea.toFixed(2));

      return;

    }, 2000)
  }

  importerList: any = [];
  importerPriceList: any = [];



  getPrice(co: string): Observable<any[]> {
    return forkJoin({
      importerInvoices: this._services.getAllImporterInvoice(),
      importers: this._services.getAllImporter()
    }).pipe(
      map((data: any) => {
        const importerInvoices = data.importerInvoices;
        const importers = data.importers;

        const filteredInvoices = importerInvoices.filter((invoice: any) => {
          const container = invoice.impotererNo.split('/');
          return container[0] === co;
        });

        const importerList = filteredInvoices.map((invoice: any) => ({
          name: invoice.impotererName,
          importerCode: invoice.impotererNo.split('/')[1]
        }));

        const importerPriceList = importers.flatMap((importer: any) => {
          const address = importer.impoter.address; // Get the single address object
          const matchingImporter = importerList.find((q: any) =>
            q.name === address.customerName && q.importerCode === address.customerCode
          );
          if (matchingImporter) {

            return [{
              importerCode: matchingImporter.importerCode,
              price: importer.impoter.addOnPrice
            }];
          }
          return [];
        });

        return importerPriceList;
      })
    );
  }



  getAllSize() {
    this._services.getAllSizeList().subscribe((resp: any) => {
      console.log(resp);

      this.sizeList = resp;
    })
  }
  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  logSelection() {
    // this.selection.selected.forEach(s => console.log(s));
    console.log(this.selection)
  }


  inputExpenceAmount(expenceAmt: any) {
    console.log(expenceAmt.target.value)
    let expenceAmount = parseFloat(expenceAmt.target.value);


    this.totalEspPrice = expenceAmount / this.totalArea;
    this.frmContainerRecieved.get('espPrice')?.setValue(this.totalEspPrice.toFixed(2));
    const newData = ELEMENT_DATA2.map((i: any) => {
      const evkPrice = i.Rate ? (parseFloat(i.Rate) + parseFloat(this.totalEspPrice)).toFixed(2) : i.EvKPrice;
      const amount = evkPrice ? (parseFloat(i.Area) * parseFloat(evkPrice)).toFixed(2) : 0; // Initialize amount to 0 if evkPrice is falsy

      return {
        ...i,
        EvKPrice: evkPrice,
        Amount: amount
      };
    });

    this.dataSource = new MatTableDataSource(newData);
    this.ngAfterViewInit();

    return
  }

  getAllBarCode() {
    this._services.getAllContainerRecieved().subscribe((resp: any) => {

      console.log(resp)
      console.log(resp);

      // Check if resp array is not empty
      if (resp.length > 0) {
        let containerData = resp[resp.length - 1].containerItem;
        if (containerData.length > 0) {
          this.bcrNo = containerData.map((obj: any) => {
            
            console.log(parseInt(obj.GerCarpetNo))
            return parseInt(obj.GerCarpetNo)});
          debugger;
          console.log("Max GerCarpetNo:", this.bcrNo);
        } else {
          console.log("containerData array is empty");
        }
      } else {
        console.log("resp array is empty");
      }


    })
  }

  openDialog(link: string) {
    let a: any = link == '/admin/size-code' ? SizeCodeComponent : QualityDesignCodeComponent;
    const dialogRef = this.matDig.open(a, {
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {

      debugger
      if (result.sizelist.length > 0) {
        result.sizelist.map((x: any) => {
          ELEMENT_DATA2.map((z: any, i: any) => {
            if (x.size == z.Size) {
              ELEMENT_DATA2[i].SCore = x.sizeCode
            }
          })
        })
      } else {
        result.map((x: any) => {
          ELEMENT_DATA2.map((z: any, i: number) => {
            if (x.oldQualityandDesign == z.QualityDesign) {
              ELEMENT_DATA2[i].QualityDesign = x.newQualityandDesign;
              ELEMENT_DATA2[i].QCode = x.qualityCodeandDesign
            }
            if (x.oldColor == z.Color) {
              ELEMENT_DATA2[i].CCode = x.colorCode;
            }

          })
        })
      }


    })
  }
}

